<script setup lang="ts">
import { defineProps, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import {
  Button,
  Card,
  Form,
  FormItem,
  Space,
  TreeSelect, Tree, Tag, Cascader
} from 'tdesign-vue-next';
import { Input } from 'tdesign-vue-next';
import { collectByTree } from '#/views/modules/tDataClassify/api';
import {useRoute, useRouter} from "vue-router";
import WebLioHeader from "#/views/modules/webLio/WebLioHeader.vue";
import WebLioFooter from "#/views/modules/webLio/WebLioFooter.vue";
import { VbenBackTopLayout } from '@vben/layouts';

const searchForm = ref();
const formData: any = ref({});
const reqRunner = {
  collectByTree: useRequest(collectByTree, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      treeData.value = []
      realList.value = res
      for(const item of res){
        let list = []
        treeDataFormat(item,list)
        treeData.value.push(list[0])
      }
    },
  }),
  searchCollectByTree: useRequest(collectByTree, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      treeData.value = []
      for(const item of res){
        let list = []
        treeDataFormat(item,list)
        treeData.value.push(list[0])
      }
    },
  }),
};
const realList = ref()
const treeDataFormat = (node,list) => {
  let obj = {value:'',label:'',children:[]}
  obj.value = node.classifyCode
  obj.label = node.classifyName + '|' + node.num
  if(node.children){
    for(const item of node.children){
      treeDataFormat(item,obj.children)
    }
  }
  list.push(obj)
}
const reload = async (data?: any) => {
  reqRunner.collectByTree.run(formData.value);
};

const searchMethod = (data?: any) => {
  const { run, loading } = reqRunner.searchCollectByTree;
  run(formData.value);
};
const searchFormSubmit = () => {
  searchMethod();
};
const resetSearch = () => {
  formData.value={}
  searchForm.value.reset();
  reload();
};

onMounted(async () => {
  await reload();
});
const treeData = ref([]);
const router = useRouter()
const toSearch = (value) => {
  router.push({name: 'tSearchIndex', query: {CategoryCode: value}})
};
</script>

<template>
  <div class="web-lio-page">
    <div class="web-lio-content">
      <Space :size="8" class="tiny-tdesign-style-patch w-full pl-[6%] pr-[6%]" direction="vertical">
        <Card style="background-color: #0071bc1a;min-height: 130px;">
          <Form ref="searchForm" :data="formData" class="w-full" labelAlign="top" @reset="resetSearch" @submit="searchFormSubmit">
            <div class="grid w-full grid-cols-2 gap-10">
              <FormItem label="选择分类" name="secretLevel" style="font-size: 20px">
                <TreeSelect v-model="formData.parentCode" :data="realList" placeholder="请选择" clearable
                            :keys="{value: 'classifyCode', label: 'classifyName', children: 'children'}"
                            :on-change="searchFormSubmit" size="large"
                />
              </FormItem>
              <FormItem  label="类型名称" name="classifyName" style="font-size: 20px">
                <Input v-model="formData.classifyName" clearable placeholder="请输入内容" size="large"
                       :on-clear="searchFormSubmit"/>
                <div class="search-buttons">
                  <Button theme="primary" @click="searchFormSubmit" class="search-btn">
                    <img src="/static/images/05.png"/>
                  </Button>
                </div>
              </FormItem>
            </div>
          </Form>
        </Card>
        <Card style="border: 0px;">
          <h1 style="font-size: 30px; line-height: 56px;text-align: left;margin-bottom: 20px;font-weight: bold;
        font-family: BlinkMacSystemFont, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif;">
            数据分类汇总
          </h1>
          <Space>
            <div v-if="treeData.length > 0" class="ml-5">
              <Tree ref="tree" :data="treeData" :expand-level="2" line>
                <template #label="{ node }">
                  <span @click="toSearch(node.value)">{{ node.label.split('|')[0] }}</span>
                  <Tag class="tag-class" theme="primary" variant="outline" @click="toSearch(node.value)">
                    {{ node.label.split('|')[1] }}
                  </Tag>
                </template>
              </Tree>
            </div>
          </Space>
        </Card>
      </Space>
    </div>
  </div>
</template>
<style scoped>
.web-lio-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.web-lio-content {
  flex: 1;
  background: #f5f7fa;
  padding: 1rem;
}
.web-lio-content {
  flex-grow: 1;
}
::v-deep .t-form__label--top {
  margin-bottom: 10px;
}
.tag-class {
  margin-left: 10px;
  border-color: #02bfe7;
  background-color: #0071bc1a;
}
.search-buttons {
  display: flex;
  height: 46px;
}
.search-buttons {
  height: 100%;
  z-index: 10;
}
.search-btn {
  border: none;
  background: transparent;
  padding: 0 !important;
  width: 46px;
  height: 46px;
  margin-left: 4px;
}
.search-btn:hover {
  background: transparent;
}
.search-btn {
  border-radius: 8px 8px 8px 8px !important;
  width: 40px;
  height: 40px;
  background: linear-gradient(to bottom, #50A3FB, #004EA2);
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  margin-right: 4px;
}

.search-btn:hover {
  background: linear-gradient(to bottom, #4095EA, #003E92);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.search-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.search-input {
  width: 100%;
  background-color: white;
  border-radius: 4px 4px 4px 4px !important;
  border: none !important;
  height: 46px;
}

:deep(.search-input .t-input__suffix) {
  right: 46px;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
}

:deep(.search-input .t-input__suffix-clear) {
  color: #999;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 16px;
  height: 16px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  border-radius: 50%;
}

:deep(.search-input .t-input__suffix-clear:hover) {
  color: #666;
  background: transparent;
}

:deep(.search-input .t-input__inner) {
  height: 46px;
  font-size: 15px;
  padding-left: 6px;
  padding-right: 65px;
}

:deep(.search-input .t-icon.t-icon-close) {
  font-size: 14px;
  width: 14px;
  height: 14px;
}
:deep(.search-input) {
  position: relative;
  z-index: 1;
}

:deep(.search-input .t-input) {
  position: relative;
  z-index: 1;
}
.search-input {
  background-color: white;
  border-radius: 4px 4px 4px 4px !important;
  border: none !important;
  height: 46px;
}

:deep(.search-input .t-input) {
  border: none !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  height: 100%;
}

:deep(.search-input .t-input:focus-within) {
  box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.2);
}
:deep(.search-input .t-input__suffix-clear) {
  color: #999;
  font-size: 16px;
  margin-right: 46px;
  cursor: pointer;
  transition: color 0.3s ease;
}

:deep(.search-input .t-input__suffix-clear:hover) {
  color: #666;
}
</style>

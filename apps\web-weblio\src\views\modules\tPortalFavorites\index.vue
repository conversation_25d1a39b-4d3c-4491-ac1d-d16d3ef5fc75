<script setup lang="tsx">
import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import EditForm from './components/EditForm.vue';
import IndexTable from './components/IndexTable.vue';
import WebLioHeader from "#/views/modules/webLio/WebLioHeader.vue";
import WebLioFooter from "#/views/modules/webLio/WebLioFooter.vue";
import CfgEditForm from "#/views/modules/tDataBase/components/CfgEditForm.vue";
import { VbenBackTopLayout } from '@vben/layouts';

const editFormRef = ref();
const tableRef = ref();
</script>

<template>
  <div class="web-lio-page">
    <div class="web-lio-content">
      <EditForm ref="editFormRef" :out-ref="tableRef" />
      <IndexTable ref="tableRef" :edit-form-ref="editFormRef" />
    </div>
  </div>
</template>
<style scoped>
.web-lio-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.web-lio-content {
  flex: 1;
  background: #f5f7fa;
  padding: 1rem;
}
.web-lio-content {
  flex-grow: 1;
}
</style>

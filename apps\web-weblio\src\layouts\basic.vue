<script lang="ts" setup>
import type { NotificationItem } from '@vben/layouts';

import { computed, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { AuthenticationLoginExpiredModal } from '@vben/common-ui';
import { VBEN_DOC_URL, VBEN_GITHUB_URL } from '@vben/constants';
import { useWatermark } from '@vben/hooks';
import { BookOpenText, CircleHelp, MdiGithub } from '@vben/icons';
import {
  BasicLayout,
  LockScreen,
  Notification,
  UserDropdown,
  VbenBackTopLayout,
} from '@vben/layouts';
import { preferences } from '@vben/preferences';
import { useAccessStore, useUserStore } from '@vben/stores';
import { openWindow } from '@vben/utils';
import { Layout, Header, Button, Space, Row, Col } from 'tdesign-vue-next';

import { msgListByPageApi, updateAllRead } from '#/api';
import { $t } from '#/locales';
import { router } from '#/router';
import { useAuthStore } from '#/store';
import LoginForm from '#/views/_core/authentication/login.vue';
import WebLioHeader from '#/views/modules/webLio/WebLioHeader.vue';
import WebLioFooter from '#/views/modules/webLio/WebLioFooter.vue';

const route = useRoute();
const activeNav = ref(route.meta.header?.navItems?.[0]?.key);

const handleNavClick = (navKey: string) => {
  activeNav.value = navKey;
  if (navKey === 'webLio.header.nav.dataEntry') {
    router.push('/analytics');
  }
};

const notifications = ref<NotificationItem[]>([]);

const userStore = useUserStore();
const authStore = useAuthStore();
const accessStore = useAccessStore();
const { destroyWatermark, updateWatermark } = useWatermark();
const showDot = computed(() =>
  notifications.value.some((item) => !item.isRead),
);

const loadData = async () => {
  if(userStore.userInfo){
    const res: any = await msgListByPageApi({
      pageSize: 5,
      pageNum: 1,
      sorts: [],
    });

    notifications.value = res.records.map((item: any) => {
      return {
        avatar: '/mail.png',
        date: item.createTime,
        isRead: item.readStatus,
        message: item.content,
        title: item.title,
      };
    });
  }
};

const menus = computed(() => [
  {
    handler: () => {
      openWindow(VBEN_DOC_URL, {
        target: '_blank',
      });
    },
    icon: BookOpenText,
    text: $t('ui.widgets.document'),
  },
  {
    handler: () => {
      openWindow(VBEN_GITHUB_URL, {
        target: '_blank',
      });
    },
    icon: MdiGithub,
    text: 'GitHub',
  },
  {
    handler: () => {
      openWindow(`${VBEN_GITHUB_URL}/issues`, {
        target: '_blank',
      });
    },
    icon: CircleHelp,
    text: $t('ui.widgets.qa'),
  },
]);

const avatar = computed(() => {
  return userStore.userInfo?.avatar ?? preferences.app.defaultAvatar;
});

async function handleLogout() {
  await authStore.logout(false);
}

function handleNoticeClear() {
  notifications.value = [];
}

async function handleMakeAll() {
  await updateAllRead({});
  await loadData();

  // notifications.value.forEach((item) => (item.isRead = true));
}

async function resetPassword(data: any) {
  await authStore.resetPassword(data, false);
}

function handleViewAll() {
  router.push({
    path: '/MyMessage',
  });
}

watch(
  () => preferences.app.watermark,
  async (enable) => {
    if (enable) {
      await updateWatermark({
        content: `${userStore.userInfo?.username}`,
      });
    } else {
      destroyWatermark();
    }
  },
  {
    immediate: true,
  },
);

onMounted(async () => {
  await loadData();
});
</script>

<template>
  <div class="web-lio-page">
    <WebLioHeader />
    <div class="web-lio-content">
      <router-view :key="$route.fullPath"/>
    </div>
    <WebLioFooter />
    <!-- 模态框等额外组件 -->
    <AuthenticationLoginExpiredModal v-model:open="accessStore.loginExpired" :avatar>
      <LoginForm />
    </AuthenticationLoginExpiredModal>
    <VbenBackTopLayout />
  </div>
</template>

<style scoped>
.web-lio-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.web-lio-content {
  flex: 1;
  background-color: #f5f7fa;
}
</style>

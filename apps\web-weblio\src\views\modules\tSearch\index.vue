<script setup lang="tsx">
import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import ChemFormula from './components/ChemFormula.vue';
import IndexTable from './components/IndexTable.vue';
import EditForm from './components/EditForm.vue';
import ShareForm from './components/ShareForm.vue';
import {useRoute} from "vue-router";
import WebLioHeader from "#/views/modules/webLio/WebLioHeader.vue";
import WebLioFooter from "#/views/modules/webLio/WebLioFooter.vue";
import { VbenBackTopLayout } from '@vben/layouts';

const editFormRef = ref();
const shareFormRef = ref();
const chemFormulaRef = ref();
const indexTable = ref();
const moth = (data) => {
  indexTable.value.formulaSearch(data.smiles)
}
const route = useRoute()
const isAdvanced = route.query.isAdvanced;
const queryType = route.query.queryType;
const queryText = route.query.queryText;
</script>

<template>
  <div class="web-lio-page">
    <div class="web-lio-content">
      <ChemFormula ref="chemFormulaRef" :out-ref="indexTable" @moth="moth"/>
      <IndexTable ref="indexTable"
                  :chem-formula-ref="chemFormulaRef"
                  :edit-form-ref="editFormRef"
                  :share-form-ref="shareFormRef"
                  :is-advanced="isAdvanced"
                  :query-type="queryType"
                  :query-text="queryText"
      />
      <EditForm ref="editFormRef" :out-ref="indexTable"/>
      <ShareForm ref="shareFormRef" :out-ref="indexTable"/>
    </div>
  </div>
</template>

<style scoped>
.web-lio-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.web-lio-content {
  flex: 1;
  background: #f5f7fa;
  padding: 1rem;
}
.web-lio-content {
  flex-grow: 1;
}
</style>

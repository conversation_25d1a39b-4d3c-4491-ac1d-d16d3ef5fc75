<script setup lang="ts">
import DataDetailsView from '#/views/_workflow/my-task/components/DataDetailsView.vue';
import CfgEditForm from '#/views/modules/tDataBase/components/CfgEditForm.vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { ref } from 'vue';

import { revoke } from './api';
import IndexTable from './components/IndexTable.vue';

// 中间层页面 主要负责页面间的解耦以及数据传输
const indexTable = ref();
const cfgEditFormRef = ref();
const dataDetailsViewRef = ref();

const add: any = () => {
  MessagePlugin.success('部署成功');
};

const revokeClick: any = async (record: any) => {
  // modalApi.setState({ title: '编辑' });
  await revoke({ id: record.id });
  indexTable.value.refresh();
  MessagePlugin.success('撤销成功');
};
</script>

<template>
  <div class="web-lio-page">
    <div class="web-lio-content">
      <IndexTable
        ref="indexTable"
        @add="add"
        @revoke="revokeClick"
        :cfg-edit-form-ref="cfgEditFormRef"
        :data-details-view-ref="dataDetailsViewRef"
      />
      <CfgEditForm
        ref="cfgEditFormRef"
        :out-ref="indexTable"
        :is-task="true"
        :is-his="true"
      />
      <DataDetailsView ref="dataDetailsViewRef" :out-ref="indexTable" />
    </div>
  </div>
</template>
<style scoped>
.web-lio-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.web-lio-content {
  flex: 1;
  background: #ffffff;
  padding: 1rem;
}
.web-lio-content {
  flex-grow: 1;
}
</style>

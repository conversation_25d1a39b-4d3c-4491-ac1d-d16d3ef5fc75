<script setup lang="ts">
import { CaretRightSmallIcon } from 'tdesign-icons-vue-next';
import { Breadcrumb } from 'tdesign-vue-next';

defineOptions({
  name: 'Breadcrumb',
});
</script>

<template>
  <div class="breadcrumb-container">
    <Breadcrumb v-bind="$attrs">
      <template #separator>
        <CaretRightSmallIcon style="color: #fff !important" />
      </template>
    </Breadcrumb>
  </div>
</template>
<style scoped lang="less">
.breadcrumb-container {
  padding: 20px 0 !important;
  background-color: #0f569f;
  :deep(.t-breadcrumb) {
    margin: 0 auto;
  }
  :deep(.t-breadcrumb__inner-text) {
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
  }
  :deep(.t-breadcrumb__separator) {
    color: #ffffff;
  }
}
</style>

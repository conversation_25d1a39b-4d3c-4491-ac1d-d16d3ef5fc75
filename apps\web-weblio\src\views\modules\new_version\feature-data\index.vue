<script setup lang="ts">
import { isLogin } from '#/components/notLoginGo';
import { useSearchStore } from '#/store/search';
import { Breadcrumb } from '#/views/modules/new_version/components/index';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { getHomeDisplayList } from './api';

const searchStore = useSearchStore();
const router = useRouter();

// 数据类型卡片数据
const dataTypes = ref<any[]>([]);

// 处理卡片点击
const handleCardClick = (_item: any) => {
  isLogin().then(() => {
    // 设置初始分类
    searchStore.setInitCategory(_item.classifyCode);
    // 跳转搜索页面
    router.push('/search-page');
  });
};

// 处理面包屑导航
const handleBreadcrumbClick = (path: string) => {
  router.push(path);
};

// 格式化描述文本，将 \n 转换为 <br>
const formatDescription = (text: string) => {
  if (!text) return '';
  return text.replaceAll(String.raw`\n`, '<br>');
};
const state = ref();
const init = async () => {
  try {
    const res = await getHomeDisplayList({});
    const result = res?.filter((el) => +el.classifyCode === 22)?.[0] || {};
    state.value = result;
    dataTypes.value = state.value?.children?.map((item) => ({
      ...item,
      id: item.id,
      title: item.classifyName,
      description: item.classifyDescribe,
      image: item.imageFile,
    }));
  } catch (error: any) {
    console.error(error);
  }
};
onMounted(() => {
  init();
});
</script>

<template>
  <div class="feature-data-page">
    <!-- 顶部蓝色背景区域 -->
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="main-title">特色数据</h1>
        <p class="subtitle">
          融合知识图谱、大模型数据集、安全数据和知识数据的智能平台
        </p>
      </div>
    </div>
    <Breadcrumb
      :options="[{ content: '首页', value: '/' }, { content: '特色数据' }]"
      @click="handleBreadcrumbClick"
    />
    <!-- 主体内容区域 -->
    <div class="main-container">
      <!-- TDesign面包屑导航 -->

      <!-- 内容展示区域 -->
      <div class="content-section">
        <!-- 左侧图片展示 -->
        <div class="left-content">
          <div class="feature-image">
            <img
              src="/static/featureDataImgs/teseshuju.png"
              alt="特色数据"
              class="main-feature-image"
            />
          </div>
        </div>

        <!-- 右侧文字介绍 -->
        <div class="right-content">
          <h2 class="section-title">特色数据</h2>
          <div
            class="description-content"
            v-html="formatDescription(state?.classifyDescribe)"
          ></div>
        </div>
      </div>

      <!-- 数据类型卡片区域 -->
      <div class="data-types-section">
        <div class="cards-grid">
          <div
            v-for="item in dataTypes"
            :key="item.id"
            class="data-card"
            @click="handleCardClick(item)"
          >
            <div class="card-image">
              <img :src="item.image" :alt="item.title" />
            </div>
            <div class="card-content">
              <h3 class="card-title">{{ item.title }}</h3>
              <p class="card-description">{{ item.description }}</p>
              <div class="card-arrow">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    d="M6 4L10 8L6 12"
                    stroke="#2c5aa0"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.feature-data-page {
  width: 100%;
  height: 100%;
  background: #ffffff !important;
}

// 顶部蓝色背景区域
.hero-section {
  background-image: url('/static/featureDataImgs/teseshujubj.png');
  background-size: cover;
  background-position: center;
  position: relative;
  padding: 80px 0 100px;
  color: white;
  text-align: center;

  .hero-content {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;

    .main-title {
      font-weight: bold;
      font-size: 42px;
      color: #ffffff;
    }

    .subtitle {
      font-weight: 400;
      font-size: 32px;
      margin-top: 15px;
    }
  }
}

// 主体容器
.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

// 内容展示区域
.content-section {
  display: flex;
  align-items: center;
  margin-bottom: 60px;
  margin-top: 30px;
  .left-content {
    flex: 0 0 400px;

    .feature-image {
      width: 100%;
      overflow: hidden;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);

      .main-feature-image {
        width: 100%;
        height: 480px;
        object-fit: cover;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }

  .right-content {
    flex: 1;
    padding-left: 20px;

    .section-title {
      font-weight: bold;
      font-size: 28px;
      color: #000000;
      text-align: left;
      margin-bottom: 15px;
    }

    .description-content {
      font-weight: 400;
      font-size: 22px;
      color: #666666;
      white-space: pre-wrap;
      word-wrap: break-word;
      line-height: 1.8;
      text-align: left;
      overflow-wrap: break-word;
    }
  }

  .right-content {
    flex: 1;

    .image-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      grid-template-rows: 1fr 1fr;
      gap: 12px;
      height: 400px;
      overflow: hidden;

      .grid-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
        transition: transform 0.3s ease;
        border: 2px solid rgba(49, 130, 206, 0.1);
        box-sizing: border-box;

        &:hover {
          transform: scale(1.02);
          border-color: rgba(49, 130, 206, 0.3);
        }

        &.main-image {
          grid-row: span 2;
          grid-column: 1;
        }
      }
    }
  }
}

// 数据类型卡片区域
.data-types-section {
  margin-bottom: 60px;

  .cards-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;

    .data-card {
      background: #fbfbfb;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      border: 1px solid rgba(226, 232, 240, 0.8);
      display: flex;
      flex-direction: column;
      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 32px rgba(49, 130, 206, 0.2);
        border-color: rgba(49, 130, 206, 0.3);
      }

      .card-image {
        height: 180px;
        overflow: hidden;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            180deg,
            transparent 0%,
            rgba(0, 0, 0, 0.1) 100%
          );
        }

        img {
          width: 100%;
          height: 240px;
          object-fit: cover;
          transition: transform 0.3s ease;
        }
      }

      &:hover .card-image img {
        transform: scale(1.1);
      }

      .card-content {
        padding: 24px 20px;
        position: relative;
        flex: 1;
        .card-title {
          font-weight: 500;
          font-size: 24px;
          color: #0f569f;
          text-align: left;
          margin-bottom: 20px;
        }

        .card-description {
          font-weight: 400;
          font-size: 18px;
          color: #666666;
          text-align: left;
          margin-bottom: 18px;
        }

        .card-arrow {
          position: absolute;
          bottom: 30px;
          right: 20px;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(44, 90, 160, 0.1);
          border-radius: 50%;
          transition: all 0.3s ease;

          svg {
            transition: transform 0.3s ease;
          }
        }
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);

        .card-content .card-arrow {
          background: rgba(44, 90, 160, 0.2);

          svg {
            transform: translateX(2px);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .main-container {
    padding: 0 16px;
  }

  .content-section {
    flex-direction: column;
    gap: 40px;

    .right-content .image-grid {
      height: 300px;
    }
  }

  .data-types-section .cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 60px 0 80px;

    .hero-content {
      .main-title {
        font-size: 36px;
      }

      .subtitle {
        font-size: 20px;
      }
    }
  }

  .content-section {
    padding: 24px;

    .left-content .section-title {
      font-size: 28px;
    }

    .right-content .image-grid {
      height: 250px;
      grid-template-columns: 1fr;
      grid-template-rows: auto auto auto auto;

      .main-image {
        grid-row: span 1;
      }
    }
  }

  .data-types-section .cards-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .main-container {
    padding: 0 16px;
  }

  .hero-section .hero-content {
    padding: 0 16px;

    .main-title {
      font-size: 28px;
    }

    .subtitle {
      font-size: 18px;
    }
  }

  .content-section {
    padding: 20px 16px;
  }
}
</style>

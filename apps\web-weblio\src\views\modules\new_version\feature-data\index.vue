<script setup lang="ts">
import { isLogin } from '#/components/notLoginGo';
import { useSearchStore } from '#/store/search';
import { Breadcrumb } from '#/views/modules/new_version/components/index';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { getHomeDisplayList } from './api';

const searchStore = useSearchStore();
const router = useRouter();

// 数据类型卡片数据
const dataTypes = ref<any[]>([]);

// 处理卡片点击
const handleCardClick = (_item: any) => {
  isLogin().then(() => {
    // 设置初始分类
    searchStore.setInitCategory(_item.classifyCode);
    // 跳转搜索页面
    router.push('/search-page');
  });
};

// 处理面包屑导航
const handleBreadcrumbClick = (path: string) => {
  router.push(path);
};
const state = ref();
const init = async () => {
  try {
    const res = await getHomeDisplayList({});
    const result = res?.filter((el) => +el.classifyCode === 22)?.[0] || {};
    state.value = result;
    dataTypes.value = state.value?.children?.map((item) => ({
      ...item,
      id: item.id,
      title: item.classifyName,
      description: item.classifyDescribe,
      image: item.imageFile,
    }));
  } catch (error: any) {
    console.error(error);
  }
};
onMounted(() => {
  init();
});
</script>

<template>
  <div class="feature-data-page">
    <!-- 顶部蓝色背景区域 -->
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="main-title">特色数据</h1>
        <p class="subtitle">
          融合知识图谱、大模型数据集、安全数据和知识数据的智能平台
        </p>
      </div>
    </div>
    <Breadcrumb
      :options="[{ content: '首页', value: '/' }, { content: '特色数据' }]"
      @click="handleBreadcrumbClick"
    />
    <!-- 主体内容区域 -->
    <div class="main-container">
      <!-- TDesign面包屑导航 -->

      <!-- 内容展示区域 -->
      <div class="content-section">
        <!-- 左侧文字介绍 -->
        <div class="left-content">
          <h2 class="section-title">特色数据</h2>
          <div class="description-content">
            {{ state?.classifyDescribe }}
          </div>
        </div>

        <!-- 右侧图片展示 -->
        <div class="right-content">
          <div class="image-grid">
            <img
              :src="state?.imageFile"
              alt="特色数据展示1"
              class="grid-image main-image"
            />
          </div>
        </div>
      </div>

      <!-- 数据类型卡片区域 -->
      <div class="data-types-section">
        <div class="cards-grid">
          <div
            v-for="item in dataTypes"
            :key="item.id"
            class="data-card"
            @click="handleCardClick(item)"
          >
            <div class="card-image">
              <img :src="item.image" :alt="item.title" />
            </div>
            <div class="card-content">
              <h3 class="card-title">{{ item.title }}</h3>
              <p class="card-description">{{ item.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.feature-data-page {
  width: 100%;
  min-height: 100vh;
  background: #f7f8fa;
}

// 顶部蓝色背景区域
.hero-section {
  background-image: url('/static/featureDataImgs/teseshujubj.png');
  background-size: cover;
  background-position: center;
  position: relative;
  padding: 80px 0 100px;
  color: white;
  text-align: center;

  .hero-content {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;

    .main-title {
      font-weight: bold;
      font-size: 42px;
      color: #ffffff;
    }

    .subtitle {
      font-weight: 400;
      font-size: 32px;
      margin-top: 15px;
    }
  }
}

// 主体容器
.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

// 内容展示区域
.content-section {
  display: flex;
  gap: 60px;
  align-items: flex-start;
  margin-bottom: 80px;
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  .left-content {
    flex: 1.2;
    .description-content {
      font-size: 16px;
      line-height: 1.8;
      color: #4a5568;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      padding: 24px 28px;
      border-radius: 12px;
      border-left: 4px solid #3182ce;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(49, 130, 206, 0.02) 0%,
          rgba(30, 58, 138, 0.01) 100%
        );
        pointer-events: none;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
      }
    }

    .section-title {
      font-size: 32px;
      font-weight: 600;
      color: #1e3a8a;
      margin: 0 0 24px 0;
      line-height: 1.3;
    }

    .description {
      font-size: 16px;
      line-height: 1.6;
      color: #4a5568;

      > p {
        margin: 0 0 24px 0;
        font-size: 18px;
        font-weight: 500;
        color: #2d3748;
      }

      .data-categories {
        .data-item {
          margin-bottom: 16px;
          padding-left: 16px;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 8px;
            width: 4px;
            height: 4px;
            background: #3182ce;
            border-radius: 50%;
          }

          strong {
            color: #2d3748;
            font-weight: 600;
          }
        }
      }
    }
  }

  .right-content {
    flex: 1;

    .image-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      grid-template-rows: 1fr 1fr;
      gap: 12px;
      height: 400px;
      overflow: hidden;

      .grid-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
        transition: transform 0.3s ease;
        border: 2px solid rgba(49, 130, 206, 0.1);
        box-sizing: border-box;

        &:hover {
          transform: scale(1.02);
          border-color: rgba(49, 130, 206, 0.3);
        }

        &.main-image {
          grid-row: span 2;
          grid-column: 1;
        }
      }
    }
  }
}

// 数据类型卡片区域
.data-types-section {
  margin-bottom: 60px;

  .cards-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;

    .data-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      border: 1px solid rgba(226, 232, 240, 0.8);

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 32px rgba(49, 130, 206, 0.2);
        border-color: rgba(49, 130, 206, 0.3);
      }

      .card-image {
        height: 180px;
        overflow: hidden;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            180deg,
            transparent 0%,
            rgba(0, 0, 0, 0.1) 100%
          );
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }
      }

      &:hover .card-image img {
        transform: scale(1.1);
      }

      .card-content {
        padding: 24px 20px;

        .card-title {
          font-size: 20px;
          font-weight: 600;
          color: #2d3748;
          margin: 0 0 12px 0;
          text-align: center;
        }

        .card-description {
          font-size: 14px;
          line-height: 1.5;
          color: #718096;
          margin: 0;
          text-align: left;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .main-container {
    padding: 0 16px;
  }

  .content-section {
    flex-direction: column;
    gap: 40px;

    .right-content .image-grid {
      height: 300px;
    }
  }

  .data-types-section .cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 60px 0 80px;

    .hero-content {
      .main-title {
        font-size: 36px;
      }

      .subtitle {
        font-size: 20px;
      }
    }
  }

  .content-section {
    padding: 24px;

    .left-content .section-title {
      font-size: 28px;
    }

    .right-content .image-grid {
      height: 250px;
      grid-template-columns: 1fr;
      grid-template-rows: auto auto auto auto;

      .main-image {
        grid-row: span 1;
      }
    }
  }

  .data-types-section .cards-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .main-container {
    padding: 0 16px;
  }

  .hero-section .hero-content {
    padding: 0 16px;

    .main-title {
      font-size: 28px;
    }

    .subtitle {
      font-size: 18px;
    }
  }

  .content-section {
    padding: 20px 16px;
  }
}
</style>

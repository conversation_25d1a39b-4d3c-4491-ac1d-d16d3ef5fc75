<script setup lang="ts">
import { ref } from 'vue';

import IndexTable from './components/IndexTable.vue';

// 中间层页面 主要负责页面间的解耦以及数据传输
const indexTable = ref();
</script>

<template>
  <div class="web-lio-page">
    <div class="web-lio-content">
      <IndexTable ref="indexTable" />
    </div>
  </div>
</template>
<style scoped>
.web-lio-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.web-lio-content {
  flex: 1;
  background: #ffffff;
  padding: 1rem;
}
.web-lio-content {
  flex-grow: 1;
}
</style>

<script setup lang="tsx">
import FormRender from '#/views/modules/tSearch/components/formEditer/components/render/FormRender.vue';
import {
  getByCodeShare,
  getDatasetListShare,
  getLabelListShare,
} from '#/views/modules/tData/api.ts';
import { Page } from '@vben/common-ui';
import { Button, Card, Form, FormItem, Tag } from 'tdesign-vue-next';
import { defineModel, defineProps, onMounted, reactive, ref } from 'vue';
import {
  getCategorysShare,
  getOneShare as getOneShareTB,
  getRealDataShare,
} from '#/views/modules/tDataBase/api.ts';
import { getOneShare } from '#/views/modules/tDataPermission/api.ts';
import { baseDownloadFile, getDictItems } from '#/api';
import { useRoute } from 'vue-router';
import { DownloadIcon } from 'tdesign-icons-vue-next';
import { useRequest } from 'vue-hooks-plus';
import WebLioHeader from '#/views/modules/webLio/WebLioHeader.vue';
import WebLioFooter from '#/views/modules/webLio/WebLioFooter.vue';
import IndexTable from '#/views/modules/tDataBase/components/IndexTable.vue';
import CfgEditForm from '#/views/modules/tDataBase/components/CfgEditForm.vue';

/**
 * 属性定义
 */
const props = defineProps({
  /**
   * 外部传入的可操作句柄
   */
  outRef: {
    type: {
      /**
       * 外部刷新方法
       */
      reload: Function,
    },
    default: null,
  },
});

const data = ref();
const datacfg = ref();
const record = ref();
const code = ref();
const permission = ref();
const metadataCodeList = ref([]);
const isReadonly = ref(true);
const realData: any = defineModel('realData', {
  type: Array,
  default: () => [
    {
      metadata_code: '',
      content_data: '',
    },
  ],
});

const analysis = (temp?: any, item?: any) => {
  item.schema.children[0].schema.children.forEach((item1) => {
    if (isReadonly.value) {
      item1.schema.placeholder = '';
    }
    if (item1.component === 'Collapse') {
      analysis(temp, item1);
    } else {
      let isValue = true;
      for (let i = 0; i < realData.value.length; i++) {
        const key = realData.value[i];
        if (key.metadata_code == item1.schema.name) {
          if (item1.component === 'Upload') {
            temp[item1.schema.name] = JSON.parse(key.content_data);
          } else {
            temp[item1.schema.name] = key.content_data;
          }
          if (!metadataCodeList.value.includes(item1.schema.name)) {
            item1.component = 'LockedItem';
            item1.schema.baseInfo = approval.value;
          }
          item1.schema.isReadonly = isReadonly.value;
          isValue = false;
          break;
        }
      }
      if (isValue) {
        data.value[item1.schema?.name] = item1.schema.value;
        item1.schema.isReadonly = isReadonly.value;
      }
    }
  });
};
const setFormData = (list) => {
  list.forEach((item) => {
    if (item.component === 'Collapse') {
      analysis(data.value, item);
    } else {
      let isValue = true;
      for (let i = 0; i < realData.value.length; i++) {
        const key = realData.value[i];
        if (key.metadata_code == item.schema.name) {
          if (item.component === 'Upload') {
            data.value[item.schema.name] = JSON.parse(key.content_data);
          } else {
            data.value[item.schema.name] = key.content_data;
          }
          if (!metadataCodeList.value.includes(item.schema.name)) {
            item.component = 'LockedItem';
            item.schema.baseInfo = approval.value;
          }
          item.schema.isReadonly = isReadonly.value;
          isValue = false;
          break;
        }
      }
      if (isValue) {
        data.value[item.schema?.name] = item.schema.value;
        item.schema.isReadonly = isReadonly.value;
      }
    }
  });
};

/**
 * 内部静态变量定义
 */
const state = reactive({
  /**
   * 数据操作对象
   */
  tagObj: {},
});

/**
 * 初始化内部数据
 */
const initStatus = () => {
  state.tagObj = {};
};

const route = useRoute();

onMounted(async () => {
  //数据分级
  classess.value = await getDictItems('DATA_LEVEL');
  classess.value.forEach((op) => {
    if (op.value == 0 || op.value == 1) {
      op['disabled'] = true;
    }
  });
  //标签库
  labelses.value = await getLabelListShare();
  console.log('p', route.query.p);
  if (route.query.p) {
    const datas = await getOneShareTB({ rsa_code: route.query.p });
    permission.value = await getOneShare({
      rsa_code: route.query.p,
      temp_code: datas.templateCode,
      rsa_accountNumber: route.query.q,
    });
    metadataCodeList.value = permission.value.metadataCodeList;
    console.log('metadataCodeList', metadataCodeList);
    classes.value = datas.classCode ? Number(datas.classCode) : null;
    //获取分类列表
    categorys.value = await getCategorysShare(classes.value);
    category.value = JSON.parse(datas.categoryCode);
    console.log(category.value);
    //数据集
    datasets.value = await getDatasetListShare({
      categoryList: category.value,
    });
    category.value = category.value.map((item) => Number(item));
    labels.value = JSON.parse(datas.labelCode);
    dataset.value = datas.datasetCode;
    approval.value = datas;
    console.log('approval', approval);
    categoryList.value = [];
    await getCategoryList(categorys.value);
    formatData();
    state.tagObj = datas;
    code.value = datas.templateCode;
    realData.value = await getRealDataShare({
      temp_code: datas.templateCode,
      operation_code: datas.baseCode,
    });
    data.value = {};
    record.value = await getByCodeShare(code.value);
    let list = JSON.parse(record.value?.cfg);
    setFormData(list);
    console.log('list', list);
    datacfg.value = list;
  }
});
const approval = ref();
// const labelsName = ref('');
const labelsTags = ref([]);
const datasetName = ref('');
const classesName = ref('');
const categoryName = ref('');
const formatData = () => {
  let labelsTag;
  // labelsName.value = ''
  labelsTags.value = [];
  datasetName.value = '';
  classesName.value = '';
  categoryName.value = '';
  if (labels.value) {
    for (const item of labels.value) {
      for (const it of labelses.value) {
        if (item == it.value) {
          // labelsName.value += it.label + ','
          labelsTag = {};
          labelsTag['key'] = it.value;
          labelsTag['content'] = it.label;
          labelsTag['color'] = it.color;
          labelsTags.value.push(labelsTag);
        }
      }
    }
  }
  if (dataset.value) {
    for (const it of datasets.value) {
      if (dataset.value == it.value) {
        datasetName.value += it.label;
      }
    }
  }
  for (const item of category.value) {
    for (const it of categoryList.value) {
      if (item == it.value) {
        categoryName.value += it.label + ',';
      }
    }
  }
  classesName.value = classess.value[classes.value].label;
  if (categoryName.value) {
    categoryName.value = categoryName.value.slice(
      0,
      categoryName.value.length - 1,
    );
  }
};
const getCategoryList = async (list) => {
  for (const item of list) {
    if (item.children && item.children.length > 0) {
      await getCategoryList(item.children);
    }
    categoryList.value.push(item);
  }
};

const labels = ref();
const classes = ref();
const category = ref();
const labelses = ref();
const dataset = ref();
const datasets = ref();
const classess = ref();
const categorys = ref();
const categoryList = ref([]);
const downDetails = async () => {
  const { run } = reqRunner.downWord;
  run(`/tSearch/downDetailsShare`, {
    operationCode: route.query.p,
  });
};

const reqRunner = {
  downWord: useRequest(baseDownloadFile, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {},
  }),
};
</script>

<template>
  <div class="web-lio-page">
    <WebLioHeader />
    <div class="web-lio-content">
      <Page title="数据详情">
        <template #extra>
          <Button
            variant="text"
            shape="square"
            :style="{ 'margin-left': '8px' }"
            @click="downDetails"
          >
            <download-icon />
          </Button>
        </template>
        <div
          ref="contentToPrint"
          class="border-1 flex h-full w-full flex-col overflow-auto rounded-lg pl-[6%] pr-[6%]"
        >
          <Card class="top-card">
            <Form ref="searchForm" class="w-[95%]">
              <div class="mt-5 grid w-full grid-cols-3 gap-1">
                <FormItem label="分级:" name="dataType">
                  {{ classesName }}
                </FormItem>
                <FormItem label="分类:" name="code">
                  {{ categoryName }}
                </FormItem>
                <FormItem label="归属数据集:" name="dataset">
                  {{ datasetName }}
                </FormItem>
                <FormItem label="标签:" name="labels">
                  <!--                  {{ labelsName }}-->
                  <Tag
                    v-for="item in labelsTags"
                    :key="item.key"
                    :color="item.color"
                    variant="light-outline"
                    style="margin-right: 10px"
                  >
                    {{ item.content }}
                  </Tag>
                </FormItem>
              </div>
            </Form>
          </Card>
          <Card style="margin-top: 10px">
            <Form
              ref="form"
              :data="data"
              :colon="true"
              class="gap-3"
              label-align="right"
            >
              <div
                class="bg-background flex w-full flex-col rounded-lg shadow"
                style="position: relative"
              >
                <div class="flex-1 rounded-lg p-2">
                  <FormRender
                    v-model="data"
                    :form-config="datacfg"
                    class="bg-background flex w-full flex-col gap-2 p-2"
                  />
                </div>
              </div>
            </Form>
          </Card>
        </div>
      </Page>
    </div>
    <WebLioFooter />
  </div>
</template>

<style scoped>
.top-card {
  background-image: url('/static/images/top-card.jpeg');
  background-size: cover;
}
.web-lio-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.web-lio-content {
  flex: 1;
  padding: 1rem;
  background: #f5f7fa;
}

.web-lio-content {
  flex-grow: 1;
}

::v-deep .t-input.t-is-readonly {
  border: none !important;
  box-shadow: none !important;
}

::v-deep .t-textarea__inner {
  resize: none;
  border: none !important;
  box-shadow: none !important;
}
</style>

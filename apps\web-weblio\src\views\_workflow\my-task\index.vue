<script setup lang="ts">
import CfgEditForm from '#/views/modules/tDataBase/components/CfgEditForm.vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { ref } from 'vue';

import { claimTask, executeTask, rejectTask } from './api';
import DataDetailsView from './components/DataDetailsView.vue';
import IndexTable from './components/IndexTable.vue';

// 中间层页面 主要负责页面间的解耦以及数据传输
const indexTable = ref();

const add: any = async () => {};

const edit: any = async (record: any) => {
  await executeTask({
    id: record.taskId,
    args: record.arg,
  });
  MessagePlugin.success('审批通过！');
  indexTable.value.refresh();
};

const claim: any = async (record: any) => {
  // modalApi.setState({ title: '编辑' });
  await claimTask({
    id: record.taskId,
  });
  MessagePlugin.success('认领成功！');
  indexTable.value.refresh();
};

const remove: any = async (record: any) => {
  await rejectTask({
    id: record.taskId,
    args: record.arg,
  });
  MessagePlugin.success('驳回成功！');
  indexTable.value.refresh();
};

const removeAndCancel: any = async (record: any) => {
  await rejectTask({
    id: record.taskId,
    args: record.arg,
  });
  MessagePlugin.success('驳回成功！');
  indexTable.value.refresh();
};
const tableRef = ref();
const cfgEditFormRef = ref();
const dataDetailsViewRef = ref();
</script>
<template>
  <div class="web-lio-page">
    <div class="web-lio-content">
      <CfgEditForm
        ref="cfgEditFormRef"
        :out-ref="tableRef"
        :is-task="true"
        @add="add"
        @claim-task="claim"
        @edit="edit"
        @remove="removeAndCancel"
      />
      <IndexTable
        ref="indexTable"
        @add="add"
        @claim-task="claim"
        @edit="edit"
        @remove="remove"
        :cfg-edit-form-ref="cfgEditFormRef"
        :data-details-view-ref="dataDetailsViewRef"
      />
      <DataDetailsView ref="dataDetailsViewRef" :out-ref="tableRef" />
    </div>
  </div>
</template>
<style scoped>
.web-lio-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.web-lio-content {
  flex: 1;
  background: #ffffff;
  padding: 1rem;
}
.web-lio-content {
  flex-grow: 1;
}
</style>
